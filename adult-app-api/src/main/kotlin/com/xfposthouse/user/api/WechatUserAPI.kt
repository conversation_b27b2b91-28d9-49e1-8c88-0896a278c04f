package com.xfposthouse.user.api

import cn.hutool.core.lang.Snowflake
import cn.hutool.http.HttpUtil
import cn.hutool.json.JSONUtil
import com.mybatisflex.core.query.QueryWrapper
import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.ThirdUserMapper
import com.xfposthouse.mapper.UserMapper
import com.xfposthouse.module.ledger.LedgerAPI
import com.xfposthouse.module.ledger.LedgerAddBookParam
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import com.xfposthouse.user.entity.Default_Password
import com.xfposthouse.user.entity.ThirdUserEntity
import com.xfposthouse.user.entity.XFUserEntity
import com.xfposthouse.user.entity.XFUserTokenEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.RestController
import java.util.*


/**
 * <AUTHOR>
 * @since 2025-06-23
 */

const val kWxAccessTokenApi = "https://api.weixin.qq.com/sns/oauth2/access_token"

const val kWxUserInfoApi = "https://api.weixin.qq.com/sns/userinfo"

@RestController
class WechatUserAPI {

    @Autowired
    lateinit var thirdUserMapper: ThirdUserMapper

    @Autowired
    lateinit var userMapper: UserMapper

    @Autowired
    lateinit var ledgerAPI: LedgerAPI

    val wxAppId = "wx09dd677e14062a07"

    val wxAppSecret = "fcd511ee22d3db23afe8eb559adaf5bd"

    /**
     * 微信登录
     */
    fun login(code: String): XFResponseEntity {

        val result = XFResponseEntity()
        val tokenEntity = XFUserTokenEntity()
        var user = XFUserEntity()

//        val thirdAccessToken = ThirdUserEntity()
//        thirdAccessToken.openid = "1231293u910212121123"
        val thirdAccessToken = getWxAccessToken(code)
        val openid: String? = thirdAccessToken?.openid
        if (thirdAccessToken == null) {
            result.setError(XFErrorCode.USER_AUTH_ERROR)
            return result
        }
        // 查询是否有绑定的openid用户
        val queryWrapper: QueryWrapper = QueryWrapper.create()
            .eq(ThirdUserEntity::openid,openid)
        var wechatUser = thirdUserMapper.selectOneByQuery(queryWrapper)
        if (Objects.isNull(wechatUser)) {
            // 新用户 - 获取微信用户信息
            wechatUser = getWxUserInfo(thirdAccessToken)
            user = createNewUser(wechatUser)
            wechatUser.userId = user.userId
            thirdUserMapper.insert(wechatUser)
        } else {
            // 老用户
           val userByUserId = userMapper.getUserByUserId(wechatUser.userId)
            if (userByUserId == null) {
               // 数据库中用户不存在
                user = createNewUser(wechatUser)
                wechatUser.userId = user.userId
                thirdUserMapper.update(wechatUser)
            } else {
                user = userByUserId
            }
            if (user.userState == 3) {
                result.setError(XFErrorCode.USER_UN_REGISTER)
                return result
            }
        }
        val token = JWTManager.instance.createJWT(user.userId.toString(), Default_Password)
        tokenEntity.userInfo = user
        tokenEntity.token = token
        result.result = tokenEntity
        return result
    }

    private fun getWxAccessToken(code: String) : ThirdUserEntity? {
        val response = HttpUtil.get("$kWxAccessTokenApi?appid=$wxAppId&secret=$wxAppSecret&code=$code&grant_type=authorization_code")
        if (!response.isNullOrEmpty()) {
            val thirdUser = ThirdUserEntity()
            val jsonObj = JSONUtil.parseObj(response)
            val accessToken = jsonObj.getStr("access_token")
            if (accessToken != null) {
                thirdUser.accessToken = accessToken
                thirdUser.openid = jsonObj.getStr("openid")
                thirdUser.unionid = jsonObj.getStr("unionid")
                thirdUser.refreshToken = jsonObj.getStr("refresh_token")
                thirdUser.type = 3
                return thirdUser
            }
            println(response)
        }
        return null
    }

    /**
     * 获取微信用户信息
     */
    private fun getWxUserInfo(thirdUserEntity: ThirdUserEntity): ThirdUserEntity {
        val accessToken = thirdUserEntity.accessToken
        val openid = thirdUserEntity.openid
        val userRes = HttpUtil.get("$kWxUserInfoApi?access_token=$accessToken&openid=$openid")
        val jsonObj = JSONUtil.parseObj(userRes)
        thirdUserEntity.nickname = jsonObj.getStr("nickname")
        thirdUserEntity.sex = jsonObj.getBool("sex")
        thirdUserEntity.avatar = jsonObj.getStr("headimgurl")
        return thirdUserEntity
    }

    /**
     * 创建新用户
     * @param [password] 密码
     * @return [XFUserEntity]
     */
    fun createNewUser(thirdUserEntity: ThirdUserEntity): XFUserEntity {
        // 创建新用户
        val user = XFUserEntity()
        val snowflake = Snowflake(1, 1)
        val snowflakeId = snowflake.nextId()
        user.userName = "wx_" + snowflakeId.toString(36)
        user.totalSpaceSize = Default_User_Space
        user.totalVoiceCount = Default_User_Voice_Count
        user.avatar = "https://file-xfpost-1317756102.cos.ap-shanghai.myqcloud.com/user/defaultAvatar.png"
        user.registerTime = System.currentTimeMillis()
        user.nickName = thirdUserEntity.nickname ?: ("用户-" + (Math.random() * 1000).toInt().toString())
        user.avatar = thirdUserEntity.avatar ?: "https://file-xfpost-1317756102.cos.ap-shanghai.myqcloud.com/user/defaultAvatar.png"
        userMapper.insertUser(user, Default_Password)
        createDefaultLedgerBook(user.userId)
        return user
    }

    /**
     * 创建默认账本
     * */
    private fun createDefaultLedgerBook(userId: Int) {
        val book = LedgerAddBookParam()
        book.name = "家庭账簿"
        ledgerAPI.addBook(book, userId.toLong())
    }
}
