package com.xfposthouse.mapper

import com.mybatisflex.core.BaseMapper
import com.xfposthouse.module.life_record.entity.LifeCommentsEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository

@Repository
interface LifeCommentsMapper : BaseMapper<LifeCommentsEntity> {

    // 定义结果映射
    @Select("SELECT * FROM life_comment WHERE user_id = #{userId} ORDER BY created_at DESC")
    @Results(
            id = "lifeCommentsMap", // 为结果映射定义一个名称
            value = [
                Result(property = "id", column = "id"),
                Result(property = "postId", column = "post_id"),
                Result(property = "userId", column = "user_id"),
                Result(property = "toUserId", column = "to_user_id"),
                Result(property = "content", column = "content"),
                Result(property = "parentId", column = "parent_id"),
                Result(property = "status", column = "status"),
                Result(property = "createdAt", column = "created_at"),
                Result(property = "updatedAt", column = "updated_at"),
                Result(property = "userName", column = "user_name"),
                Result(property = "userAvatar", column = "user_avatar"),
                Result(property = "toUserName", column = "to_user_name"),
            ]
    )
    fun getCommentsByUserId(@Param("userId") userId: Long): List<LifeCommentsEntity>

    // 插入评论
    @Insert("INSERT INTO life_comment (post_id, user_id, content, parent_id, status, created_at, updated_at) " +
            "VALUES (#{postId}, #{userId}, #{content}, #{parentId}, #{status}, #{createdAt}, #{createdAt})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertComments(reply: LifeCommentsEntity): Int

    // 插入回复
    @Insert("INSERT INTO life_reply (user_id, content, parent_id, status, created_at, updated_at, to_user_id) " +
            "VALUES (#{userId}, #{content}, #{parentId}, #{status}, #{createdAt}, #{createdAt}, #{toUserId})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    fun insertReply(reply: LifeCommentsEntity): Int

    // 更新评论
    @Update("UPDATE life_comment SET content = #{content}, status = #{status}, updated_at = #{updatedAt} WHERE id = #{id}")
    fun updateComments(reply: LifeCommentsEntity): Int

    // 删除评论
    @Delete("DELETE FROM life_comment WHERE id = #{id}")
    fun deleteComments(@Param("id") id: Long): Int

    // 查询单条评论
    @Select("SELECT * FROM life_comment WHERE id = #{id}")
    @ResultMap("lifeCommentsMap") // 复用结果映射
    fun getCommentsById(@Param("id") id: Long): LifeCommentsEntity?

    // 查询帖子的所有评论
    @Select("""
        SELECT c.*, u.nick_name as user_name, u.avatar as user_avatar
        FROM life_comment c
        JOIN users u ON c.user_id = u.uid
        WHERE c.post_id = #{postId} ORDER BY c.created_at DESC
        LIMIT #{pageSize} OFFSET #{offset}
        """)
    @ResultMap("lifeCommentsMap") // 复用结果映射
    fun getCommentsByPostId(@Param("postId") postId: Long,
                            @Param("offset") offset: Int,
                            @Param("pageSize") pageSize: Int): List<LifeCommentsEntity>

    // 统计帖子评论数量（带缓存）
    @Select("SELECT COUNT(*) FROM life_comment WHERE post_id = #{postId}")
    @Options(useCache = true, flushCache = Options.FlushCachePolicy.FALSE)
    fun countByPostId(@Param("postId") postId: Long?): Int

    @Select("SELECT COUNT(*) FROM life_reply WHERE parent_id = #{commentId}")
    @Options(useCache = true, flushCache = Options.FlushCachePolicy.FALSE)
    fun replyCountByCommentId(@Param("commentId") commentId: Long?): Int

    @Select("""
        SELECT c.*, u.nick_name as user_name, u.avatar as user_avatar, t.nick_name as to_user_name
        FROM life_reply c
        JOIN users u ON c.user_id = u.uid
        JOIN users t ON c.to_user_id = t.uid
        WHERE c.parent_id = #{parentId} ORDER BY c.created_at DESC
        LIMIT #{pageSize} OFFSET #{page}
        """)
    @ResultMap("lifeCommentsMap") // 复用结果映射
    fun getRepliesByParentId(@Param("parentId") parentId: Long,
                            @Param("page") page: Int,
                            @Param("pageSize") pageSize: Int): List<LifeCommentsEntity>
}
