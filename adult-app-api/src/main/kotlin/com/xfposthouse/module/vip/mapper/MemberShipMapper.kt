package com.xfposthouse.module.vip.mapper

import com.xfposthouse.module.vip.entity.MemberShipEntity
import com.xfposthouse.module.vip.entity.MembershipBenefitEntity
import org.apache.ibatis.annotations.*
import org.springframework.stereotype.Repository
import com.mybatisflex.core.BaseMapper
import java.math.BigDecimal

@Repository
interface MemberShipMapper: BaseMapper<MemberShipEntity> {

    // 查询所有会员信息
    @Select(" SELECT *  FROM membership")
    @Results(id = "membershipWithBenefitsMap", value = [
        Result(property = "membershipId", column = "membership_id"),
        Result(property = "membershipType", column = "membership_type"),
        Result(property = "membershipPlan", column = "membership_plan"),
        Result(property = "price", column = "price", javaType = BigDecimal::class),
        Result(property = "originalPrice", column = "original_price", javaType = BigDecimal::class),
        Result(property = "durationDays", column = "duration_days"),
        Result(property = "description", column = "description"),
        Result(property = "purchaseId", column = "purchase_id"),
        Result(property = "membershipLevel", column = "membership_level"),
        Result(property = "benefits", column = "membership_id", javaType = List::class,
            many = Many(select = "com.xfposthouse.module.vip.mapper.MembershipBenefitMapper.findBenefitsByMembershipId")
        )
    ])
    fun findAll(): List<MemberShipEntity>

    // 根据 ID 查询会员信息
    @Select(" SELECT * FROM membership m WHERE m.membership_id = #{membershipId}")
    @ResultMap("membershipWithBenefitsMap")
    fun findById(membershipId: Int): MemberShipEntity?

    // 插入会员信息并返回主键
    @Insert("""
        INSERT INTO membership (membership_type, membership_plan, price, original_price, duration_days, description, purchase_id, membership_level)
        VALUES (#{membershipType}, #{membershipPlan}, #{price}, #{originalPrice}, #{durationDays}, #{description}, #{purchaseId}, #{membershipLevel})
    """)
    @Options(useGeneratedKeys = true, keyProperty = "membershipId")
    fun insertMemberShip(membership: MemberShipEntity): Int



}