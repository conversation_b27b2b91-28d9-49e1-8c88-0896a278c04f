package com.xfposthouse.module.ledger

import com.mybatisflex.core.query.QueryWrapper
import com.mybatisflex.core.update.UpdateChain
import com.xfposthouse.jwt.JWTManager
import com.xfposthouse.mapper.LedgerBookMapper
import com.xfposthouse.mapper.LedgerMapper
import com.xfposthouse.network.entity.XFErrorCode
import com.xfposthouse.network.entity.XFResponseEntity
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.math.BigDecimal


@RestController
@RequestMapping("/api/ledger")
class LedgerAPI {

    @Autowired
    private lateinit var ledgerBookMapper: LedgerBookMapper

    @Autowired
    lateinit var ledgerMapper: LedgerMapper

    @Autowired
    lateinit var bookMapper: LedgerBookMapper

    @PostMapping("/add")
    fun createLedger(@RequestBody ledger: LedgerAddParam, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                val ledgerEntity = LedgerEntity(type = ledger.type, amount = BigDecimal(ledger.amount))
                ledgerEntity.userId = userId
                ledgerEntity.description = ledger.desc
                ledgerEntity.isVoice = ledger.isVoice
                /*  if (ledger.recordDate != null) {
                    ledgerEntity.recordDate = ledger.recordDate!!.toLocalDateTime()
                }*/
                if (ledger.bookId > 0) {
                    if (isExists(ledger.bookId, ledgerEntity.userId)) {
                        ledgerEntity.bookId = ledger.bookId
                        ledgerMapper.insert(ledgerEntity)
                        val amountInfo = updateLedgerBook(ledger.bookId)
                        result.result = mapOf("amountInfo" to amountInfo)
                        return result
                    }
                }
                result.setError(XFErrorCode.PARAM_INVALID)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 删除账单 */
    @PostMapping("/delete")
    fun deleteLedger(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                val id = param["id"].toString().toLong()
                val bookId = ledgerMapper.getBookIdWithLedgerId(id)?:0
                val flag = ledgerMapper.deleteById(id)
                if (flag <= 0) {
                    result.setError(XFErrorCode.PARAM_INVALID)
                } else {
                    if (bookId > 0) {
                        updateLedgerBook(bookId)
                    }
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /**
     * 创建账本
     */
    @PostMapping("/addBook")
    fun createLedgerBook(@RequestBody book: LedgerAddBookParam, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val bookName = book.name?:""
            if (bookName.isEmpty()) {
                result.setError(XFErrorCode.PARAM_INVALID)
                return result
            }
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                if ((book.id?:0) > 0) {
                    val updated = UpdateChain(ledgerBookMapper)
                        .set("name", bookName)
                        .where("id = ?", book.id)
                        .update()
                    if (!updated) {
                        result.setError(XFErrorCode.PARAM_INVALID)
                    }
                    return result
                }
                if (isExistName(bookName, userId)) {
                    result.setError(XFErrorCode.LEDGER_BOOK_EXIST)
                } else {
                    result.result = addBook(book, userId)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 获取账本列表 */
    @PostMapping("/books")
    fun getLedgerBooks(@RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                val query = QueryWrapper.create().eq("user_id", userId)
                val list = ledgerBookMapper.selectListByQuery(query)
                result.result = mapOf("list" to list)
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 删除账簿 */
    @PostMapping("/deleteBook")
    fun deleteLedgerBook(@RequestBody param: Map<String, Any>, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val id = param["id"].toString().toLong()
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                if (isExists(id, userId)) {
                    ledgerBookMapper.deleteById(id)
                } else {
                    result.setError(XFErrorCode.PARAM_INVALID)
                }
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 获取是否使用完体验次数 */
    @PostMapping("/voiceCount")
    fun getLedgerVoiceCount(@RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            val userId = JWTManager.instance.verifyToken(token).toLong()
            if (userId > 0) {
                val query = QueryWrapper.create().eq("user_id", userId).eq("is_voice", true)
                val count = ledgerMapper.selectCountByQuery(query)
                result.result = mapOf("count" to count, "canUse" to (count < 8))
            } else {
                result.setError(XFErrorCode.INVALID_TOKEN)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 获取账本明细 */
    @PostMapping("/list")
    fun getLedgers(@RequestBody param: LedgerListParam, @RequestHeader("token") token: String): XFResponseEntity {
        val result = XFResponseEntity()
        try {
            if (param.bookId > 0) {
                val userId = JWTManager.instance.verifyToken(token).toLong()
                if (userId > 0) {
                    var list = listOf<LedgerEntity>()
                    if (param.lastTime != null) {
                        /// 获取该时间后面的数据
                        val query = QueryWrapper.create()
                            .eq("user_id", userId)
                            .eq("book_id", param.bookId)
                            .gt("record_date", param.lastTime)
                            .orderBy("record_date", false)
                            .limit(16)
                        list = ledgerMapper.selectListByQuery(query)
//                        result.result = mapOf("list" to list)
                    } else if (param.firstTime != null) {
                        /// 获取该时间前面的数据
                        val query = QueryWrapper.create()
                            .eq("user_id", userId)
                            .eq("book_id", param.bookId)
                            .lt("record_date", param.firstTime)
                            .orderBy("record_date", false)
                            .limit(16)
                        list = ledgerMapper.selectListByQuery(query)
//                        result.result = mapOf("list" to list)
                    } else {
                        result.setError(XFErrorCode.PARAM_INVALID)
                    }
                    if (list.isNotEmpty()) {
                        // 获取涉及的时间范围（用于后续按月统计）
                        val maxDate = list.first().recordDate
                        val minDate = list.last().recordDate

                        val statsRaw = ledgerMapper.getMonthlyExpenseStats(
                            userId,
                            bookId = param.bookId,
                            startDate = minDate,
                            endDate = maxDate
                        )
                        result.result = mapOf("list" to list, "monthInfos" to statsRaw)
                    } else {
                        result.result = mapOf("list" to list)
                    }
                } else {
                    result.setError(XFErrorCode.INVALID_TOKEN)
                }
            } else {
                result.setError(XFErrorCode.PARAM_INVALID)
            }
            return result
        } catch (error: Error) {
            result.setError(XFErrorCode.SERVER_ERROR)
            return result
        }
    }

    /** 创建账本方法提取 */
    fun addBook(book: LedgerAddBookParam, userId: Long): LedgerBookEntity {
        val bookEntity = LedgerBookEntity()
        bookEntity.name = book.name
        bookEntity.userId = userId
        bookEntity.description = book.desc
        bookMapper.insert(bookEntity)
        return  bookEntity
    }

    /** 更新总数量 */
    private fun updateLedgerBook(bookId: Long): MutableMap<String?, BigDecimal?>? {
        val amountMap = ledgerMapper.getAmountSummary(bookId)
        val income = amountMap?.get("incomeAmount")
        val out = amountMap?.get("outAmount")
        UpdateChain(ledgerBookMapper)
            .set("income_amount", income)
            .set("out_amount", out)
            .where("id = ?", bookId)
            .update()
        return amountMap
    }

    private fun isExists(id: Long, userId: Long): Boolean {
        val query = QueryWrapper.create().eq("id", id).eq("user_id", userId)
        val result = bookMapper.selectCountByQuery(query)
        return result > 0
    }

    /** 账簿名称是否存在 */
    private fun isExistName(name: String, userId: Long): Boolean {
        val query = QueryWrapper.create().eq("name", name).eq("user_id", userId)
        val result = bookMapper.selectCountByQuery(query)
        return result > 0
    }
}