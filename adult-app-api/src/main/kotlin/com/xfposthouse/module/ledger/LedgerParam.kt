package com.xfposthouse.module.ledger

import java.time.LocalDateTime


class LedgerAddParam {
    var bookId: Long = 0
    var type: Boolean = false /// true：收入 false：支出
    var amount: Double = 0.0
    var recordDate: LocalDateTime? = null
    var desc: String? = null
    var isVoice: Boolean = false /// 是否语音记账
}

class LedgerAddBookParam {
    /** 有id时表示编辑 */
    var id: Long? = null
    var name: String? = null
    var desc: String? = null
}

class LedgerListParam {
    var bookId: Long = 0
    /** 最小时间-查询比该时间早的数据 */
    var firstTime: LocalDateTime? = null
    /** 最大时间-查询比该时间晚的数据 */
    var lastTime: LocalDateTime? = null
    var limit: Int = 16
}